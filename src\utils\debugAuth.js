/**
 * Debug utilities for authentication issues
 */

export const debugAuthState = () => {
  console.log('=== AUTH DEBUG ===');
  
  // Check localStorage
  const authToken = localStorage.getItem('auth-token');
  const authStorage = localStorage.getItem('auth-storage');
  
  console.log('LocalStorage:');
  console.log('  auth-token:', authToken ? `${authToken.substring(0, 20)}...` : 'null');
  console.log('  auth-storage:', authStorage);
  
  // Parse auth-storage if it exists
  if (authStorage) {
    try {
      const parsed = JSON.parse(authStorage);
      console.log('  parsed auth-storage:', parsed);
    } catch (e) {
      console.log('  failed to parse auth-storage:', e.message);
    }
  }
  
  // Check Zustand store
  try {
    const { useAuthStore } = require('../stores/authStore');
    const state = useAuthStore.getState();
    console.log('Zustand Store:');
    console.log('  isAuthenticated:', state.isAuthenticated);
    console.log('  currentUser:', state.currentUser);
    console.log('  token:', state.token ? `${state.token.substring(0, 20)}...` : 'null');
    console.log('  _initialized:', state._initialized);
  } catch (e) {
    console.log('Failed to access Zustand store:', e.message);
  }
  
  console.log('=== END AUTH DEBUG ===');
};

// Make it available globally for debugging
if (typeof window !== 'undefined') {
  window.debugAuth = debugAuthState;
}
