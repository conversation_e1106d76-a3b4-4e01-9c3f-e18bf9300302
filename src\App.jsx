import React, { useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import "./styles/App.css";
import { LoginPage } from "./pages";
import Dashboard from "./pages/Dashboard";
import { ErrorBoundary } from "./components/common";
import { PrivateRoute, PublicRoute } from "./routes";
import { useAuthStore } from "./stores";
import { useErrorHandler } from "./hooks";
import { ToastProvider } from "./contexts/ToastContext";
import AuthDebugger from "./components/debug/AuthDebugger";

// Main App content component (for testing)
export function AppContent({ config }) {
  const { currentUser, isAuthenticated, initializeAuth } = useAuthStore();
  const { error, handleError, clearError, addError } = useErrorHandler();

  // Initialize auth state on app load - only once
  useEffect(() => {
    console.log('App: Initializing auth state...');

    // Only initialize if we haven't already
    if (!isAuthenticated && !currentUser) {
      const timer = setTimeout(() => {
        initializeAuth();
      }, 100); // Small delay to allow Zustand persistence to hydrate

      return () => clearTimeout(timer);
    } else {
      console.log('App: Auth already initialized, skipping');
    }
  }, []); // Empty dependency array to run only once

  // Log the config for debugging
  useEffect(() => {
    console.log('App config:', config);
  }, [config]);

  return (
    <ToastProvider>
      <ErrorBoundary
        message={null} // Let ErrorBoundary use the actual error message
        onError={(error, errorInfo) => {
          console.error("App Error Boundary:", error, errorInfo);
          addError({
            id: Date.now(),
            message: error.message || "An error occurred. Please refresh the page.",
            type: "error",
          });
        }}
      >
        <Routes>
          <Route
            path="/login"
            element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            }
          />
          <Route
            path="/dashboard"
            element={
              <PrivateRoute>
                <Dashboard />
              </PrivateRoute>
            }
          />
          <Route
            path="/"
            element={<Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />}
          />
        </Routes>
        <AuthDebugger />
      </ErrorBoundary>
    </ToastProvider>
  );
}

// Wrapper component for production use
function AppWithRouter({ config }) {
  return (
    <Router>
      <AppContent config={config} />
    </Router>
  );
}

export default AppWithRouter;
