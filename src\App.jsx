import React, { useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import "./styles/App.css";
import { LoginPage } from "./pages";
import Dashboard from "./pages/Dashboard";
import { ErrorBoundary } from "./components/common";
import { PrivateRoute, PublicRoute } from "./routes";
import { useAuthStore } from "./stores";
import { useErrorHandler } from "./hooks";
import { ToastProvider } from "./contexts/ToastContext";
import AuthDebugger from "./components/debug/AuthDebugger";

// Main App content component (for testing)
export function AppContent({ config }) {
  const { currentUser, isAuthenticated, initializeAuth } = useAuthStore();
  const { error, handleError, clearError, addError } = useErrorHandler();

  // Temporarily disable initialization to debug the issue
  useEffect(() => {
    console.log('App: Skipping auth initialization for debugging');
    console.log('App: Current auth state:', { isAuthenticated, hasUser: !!currentUser });
  }, [isAuthenticated, currentUser]);

  // Log the config for debugging
  useEffect(() => {
    console.log('App config:', config);
  }, [config]);

  return (
    <ToastProvider>
      <ErrorBoundary
        message={null} // Let ErrorBoundary use the actual error message
        onError={(error, errorInfo) => {
          console.error("App Error Boundary:", error, errorInfo);
          addError({
            id: Date.now(),
            message: error.message || "An error occurred. Please refresh the page.",
            type: "error",
          });
        }}
      >
        <Routes>
          <Route
            path="/login"
            element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            }
          />
          <Route
            path="/dashboard"
            element={
              <PrivateRoute>
                <Dashboard />
              </PrivateRoute>
            }
          />
          <Route
            path="/"
            element={<Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />}
          />
        </Routes>
        {/* <AuthDebugger /> Temporarily disabled to prevent infinite loops */}
      </ErrorBoundary>
    </ToastProvider>
  );
}

// Wrapper component for production use
function AppWithRouter({ config }) {
  return (
    <Router>
      <AppContent config={config} />
    </Router>
  );
}

export default AppWithRouter;
