import { useEffect, useState } from 'react';
import { useAuthStore } from '../../stores/authStore';

/**
 * Debug component to monitor auth state changes
 * Only renders in development mode
 */
const AuthDebugger = () => {
  const authState = useAuthStore();
  const [logs, setLogs] = useState([]);

  useEffect(() => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
      timestamp,
      isAuthenticated: authState.isAuthenticated,
      hasUser: !!authState.currentUser,
      hasToken: !!authState.token,
      userEmail: authState.currentUser?.email,
      isLoading: authState.isLoading,
      error: authState.error
    };

    setLogs(prev => [...prev.slice(-4), logEntry]); // Keep last 5 logs
    
    console.log('Auth State Change:', logEntry);
  }, [
    authState.isAuthenticated, 
    authState.currentUser, 
    authState.token, 
    authState.isLoading, 
    authState.error
  ]);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      left: '10px',
      background: 'rgba(0, 0, 0, 0.8)',
      color: 'white',
      padding: '12px',
      borderRadius: '8px',
      fontSize: '12px',
      fontFamily: 'monospace',
      maxWidth: '400px',
      zIndex: 9999,
      maxHeight: '300px',
      overflow: 'auto'
    }}>
      <h4 style={{ margin: '0 0 8px 0', color: '#00ff00' }}>🔍 Auth Debugger</h4>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Current State:</strong>
        <div>Authenticated: {authState.isAuthenticated ? '✅' : '❌'}</div>
        <div>User: {authState.currentUser?.email || 'None'}</div>
        <div>Token: {authState.token ? '✅ Present' : '❌ Missing'}</div>
        <div>Loading: {authState.isLoading ? '⏳' : '✅'}</div>
        {authState.error && <div style={{ color: '#ff6b6b' }}>Error: {authState.error}</div>}
      </div>

      <div>
        <strong>Recent Changes:</strong>
        {logs.map((log, index) => (
          <div key={index} style={{ 
            fontSize: '10px', 
            opacity: 0.7 + (index * 0.1),
            borderLeft: '2px solid #333',
            paddingLeft: '4px',
            marginTop: '2px'
          }}>
            <span style={{ color: '#888' }}>{log.timestamp}</span> - 
            Auth: {log.isAuthenticated ? '✅' : '❌'}, 
            User: {log.hasUser ? '✅' : '❌'}, 
            Token: {log.hasToken ? '✅' : '❌'}
            {log.error && <span style={{ color: '#ff6b6b' }}> | Error: {log.error}</span>}
          </div>
        ))}
      </div>

      <div style={{ marginTop: '8px', fontSize: '10px', opacity: 0.6 }}>
        LocalStorage Token: {localStorage.getItem('auth-token') ? '✅' : '❌'}
      </div>
    </div>
  );
};

export default AuthDebugger;
