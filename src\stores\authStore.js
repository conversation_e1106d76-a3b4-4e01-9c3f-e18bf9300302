import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      currentUser: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (email, password) => {
        set({ isLoading: true, error: null });
        
        try {
          // Mock login for admin portal
          // In a real app, this would make an API call
          if (email && password) {
            const mockUser = {
              id: '1',
              email: email,
              name: 'Admin User',
              role: 'admin'
            };
            
            set({
              currentUser: mockUser,
              isAuthenticated: true,
              isLoading: false,
              error: null
            });
            
            return { success: true };
          } else {
            throw new Error('Invalid credentials');
          }
        } catch (error) {
          set({
            isLoading: false,
            error: error.message || 'Login failed'
          });
          return { success: false, error: error.message || 'Login failed' };
        }
      },

      logout: () => {
        set({
          currentUser: null,
          isAuthenticated: false,
          error: null
        });
      },

      clearError: () => {
        set({ error: null });
      },

      // Update user data
      updateUser: (userData) => {
        set((state) => ({
          currentUser: { ...state.currentUser, ...userData }
        }));
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        currentUser: state.currentUser,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
);

export { useAuthStore };
