import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authAPI } from '../services/api';
import { encryptPassword } from '../utils/crypto';

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      currentUser: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      token: null,
      _initialized: false,

      // Actions
      login: async (email, password) => {
        set({ isLoading: true, error: null });

        try {
          // Encrypt the password before sending to API
          const encryptedPassword = await encryptPassword(password);

          // Call the real API with encrypted password
          const response = await authAPI.signIn({
            email,
            password: encryptedPassword
          });

          // Extract token and user data from the actual API response structure
          const token = response.data?.token;
          const userData = response.data;

          if (!token) {
            throw new Error('No authentication token received');
          }

          // Store token in localStorage for API interceptor
          localStorage.setItem('auth-token', token);

          const user = {
            id: userData.userId,
            email: userData.userEmailId || email,
            name: userData.name || userData.fullName || 'Admin User',
            role: 'admin',
            portalType: 'ADMIN',
            isProfileSet: userData.is_profile_set || false,
            authorities: userData.authorities || []
          };

          set({
            currentUser: user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
            token: token
          });

          // Manual persistence as backup
          try {
            const authData = {
              currentUser: user,
              isAuthenticated: true,
              token: token,
              timestamp: Date.now()
            };
            localStorage.setItem('manual-auth-backup', JSON.stringify(authData));
            console.log('Manual auth backup saved');
          } catch (e) {
            console.error('Failed to save manual auth backup:', e);
          }

          console.log('Auth store: Login successful, state updated:', {
            isAuthenticated: true,
            userId: user.id,
            email: user.email,
            hasToken: !!token
          });

          // Verify localStorage immediately after setting
          console.log('LocalStorage verification:', {
            'auth-token': localStorage.getItem('auth-token'),
            'auth-storage': localStorage.getItem('auth-storage'),
            'manual-auth-backup': localStorage.getItem('manual-auth-backup')
          });

          return { success: true, user, token };
        } catch (error) {
          console.error('Login error:', error);

          // Clear any stored token on error
          localStorage.removeItem('auth-token');

          const errorMessage = error.message || 'Login failed. Please check your credentials.';

          set({
            isLoading: false,
            error: errorMessage,
            currentUser: null,
            isAuthenticated: false,
            token: null
          });

          return { success: false, error: errorMessage };
        }
      },

      logout: async () => {
        try {
          // Call logout API (optional - even if it fails, we clear local state)
          await authAPI.signOut();
        } catch (error) {
          console.warn('Logout API call failed:', error.message);
        } finally {
          // Always clear local storage and state
          localStorage.removeItem('auth-token');
          localStorage.removeItem('auth-storage');

          set({
            currentUser: null,
            isAuthenticated: false,
            error: null,
            token: null
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // Update user data
      updateUser: (userData) => {
        set((state) => ({
          currentUser: { ...state.currentUser, ...userData }
        }));
      },

      // Initialize auth state from stored token
      initializeAuth: async () => {
        const storedState = get();

        // Prevent multiple initializations
        if (storedState._initialized) {
          console.log('Auth already initialized, skipping');
          return;
        }

        const token = localStorage.getItem('auth-token');

        console.log('Initializing auth state...', {
          hasToken: !!token,
          isAuthenticated: storedState.isAuthenticated,
          hasCurrentUser: !!storedState.currentUser,
          isLoading: storedState.isLoading
        });

        // Mark as initialized to prevent multiple calls
        set({ _initialized: true });

        // Try to restore from manual backup if Zustand persistence failed
        try {
          const manualBackup = localStorage.getItem('manual-auth-backup');
          if (manualBackup && token) {
            const backupData = JSON.parse(manualBackup);
            const isRecent = Date.now() - backupData.timestamp < 24 * 60 * 60 * 1000; // 24 hours

            if (isRecent && backupData.isAuthenticated && backupData.currentUser) {
              console.log('Restoring from manual backup');
              set({
                currentUser: backupData.currentUser,
                isAuthenticated: true,
                token: token,
                error: null
              });
              return;
            }
          }
        } catch (e) {
          console.warn('Failed to restore from manual backup:', e);
        }

        // If we're currently loading (login in progress), don't interfere
        if (storedState.isLoading) {
          console.log('Login in progress, skipping initialization');
          return;
        }

        // If we already have a valid auth state, don't interfere
        if (storedState.isAuthenticated && storedState.currentUser && token) {
          console.log('Auth state already valid, skipping initialization');
          return;
        }

        // If we have a token and persisted user data, restore the session
        if (token && storedState.currentUser) {
          console.log('Restoring auth session from stored state');
          set({
            isAuthenticated: true,
            token: token,
            currentUser: storedState.currentUser
          });
          return;
        }

        // Only clear state if we're sure there's no valid session
        if (!token && !storedState.currentUser && !storedState.isAuthenticated) {
          console.log('No valid session found, ensuring auth state is cleared');
          set({
            currentUser: null,
            isAuthenticated: false,
            token: null,
            error: null
          });
        } else {
          console.log('Ambiguous state, leaving as is to avoid disrupting active session');
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        currentUser: state.currentUser,
        isAuthenticated: state.isAuthenticated,
        token: state.token
      })
    }
  )
);

export { useAuthStore };
