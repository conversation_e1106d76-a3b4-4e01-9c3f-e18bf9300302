import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authAPI } from '../services/api';
import { encryptPassword } from '../utils/crypto';

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      currentUser: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      token: null,

      // Actions
      login: async (email, password) => {
        set({ isLoading: true, error: null });

        try {
          // Encrypt the password before sending to API
          const encryptedPassword = await encryptPassword(password);

          // Call the real API with encrypted password
          const response = await authAPI.signIn({
            email,
            password: encryptedPassword
          });

          // Extract token and user data from the actual API response structure
          const token = response.data?.token;
          const userData = response.data;

          if (!token) {
            throw new Error('No authentication token received');
          }

          // Store token in localStorage for API interceptor
          localStorage.setItem('auth-token', token);

          const user = {
            id: userData.userId,
            email: userData.userEmailId || email,
            name: userData.name || userData.fullName || 'Admin User',
            role: 'admin',
            portalType: 'ADMIN',
            isProfileSet: userData.is_profile_set || false,
            authorities: userData.authorities || []
          };

          set({
            currentUser: user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
            token: token
          });

          console.log('Auth store: Login successful, state updated:', {
            isAuthenticated: true,
            userId: user.id,
            email: user.email,
            hasToken: !!token
          });

          return { success: true, user, token };
        } catch (error) {
          console.error('Login error:', error);

          // Clear any stored token on error
          localStorage.removeItem('auth-token');

          const errorMessage = error.message || 'Login failed. Please check your credentials.';

          set({
            isLoading: false,
            error: errorMessage,
            currentUser: null,
            isAuthenticated: false,
            token: null
          });

          return { success: false, error: errorMessage };
        }
      },

      logout: async () => {
        try {
          // Call logout API (optional - even if it fails, we clear local state)
          await authAPI.signOut();
        } catch (error) {
          console.warn('Logout API call failed:', error.message);
        } finally {
          // Always clear local storage and state
          localStorage.removeItem('auth-token');
          localStorage.removeItem('auth-storage');

          set({
            currentUser: null,
            isAuthenticated: false,
            error: null,
            token: null
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // Update user data
      updateUser: (userData) => {
        set((state) => ({
          currentUser: { ...state.currentUser, ...userData }
        }));
      },

      // Initialize auth state from stored token
      initializeAuth: async () => {
        const token = localStorage.getItem('auth-token');
        const storedState = get();

        console.log('Initializing auth state...', {
          hasToken: !!token,
          isAuthenticated: storedState.isAuthenticated,
          hasCurrentUser: !!storedState.currentUser
        });

        // If we already have a valid auth state, don't interfere
        if (storedState.isAuthenticated && storedState.currentUser && token) {
          console.log('Auth state already valid, skipping initialization');
          return;
        }

        if (!token) {
          console.log('No token found, ensuring auth state is cleared');
          set({
            currentUser: null,
            isAuthenticated: false,
            token: null,
            error: null
          });
          return;
        }

        // If we have a token and persisted user data, restore the session
        if (token && storedState.currentUser) {
          console.log('Restoring auth session from stored state');
          set({
            isAuthenticated: true,
            token: token,
            currentUser: storedState.currentUser
          });
          return;
        }

        // If we only have a token but no user data, it might be stale
        // For now, let's be conservative and clear it
        console.log('Token found but no user data, clearing stale session');
        localStorage.removeItem('auth-token');
        set({
          currentUser: null,
          isAuthenticated: false,
          token: null,
          error: null
        });
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        currentUser: state.currentUser,
        isAuthenticated: state.isAuthenticated,
        token: state.token
      })
    }
  )
);

export { useAuthStore };
