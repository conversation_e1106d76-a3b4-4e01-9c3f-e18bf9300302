import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authAPI } from '../services/api';
import { encryptPassword } from '../utils/crypto';

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      currentUser: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      token: null,

      // Actions
      login: async (email, password) => {
        set({ isLoading: true, error: null });

        try {
          // Encrypt the password before sending to API
          const encryptedPassword = await encryptPassword(password);

          // Call the real API with encrypted password
          const response = await authAPI.signIn({
            email,
            password: encryptedPassword
          });

          // Extract token and user data from response
          const token = response.token || response.accessToken || response.jwt;
          const userData = response.user || response.data || {
            email: email,
            role: 'admin',
            portalType: 'ADMIN'
          };

          if (!token) {
            throw new Error('No authentication token received');
          }

          // Store token in localStorage for API interceptor
          localStorage.setItem('auth-token', token);

          const user = {
            id: userData.id || Date.now().toString(),
            email: userData.email || email,
            name: userData.name || userData.fullName || 'Admin User',
            role: userData.role || 'admin',
            portalType: 'ADMIN'
          };

          set({
            currentUser: user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
            token: token
          });

          return { success: true, user, token };
        } catch (error) {
          console.error('Login error:', error);

          // Clear any stored token on error
          localStorage.removeItem('auth-token');

          const errorMessage = error.message || 'Login failed. Please check your credentials.';

          set({
            isLoading: false,
            error: errorMessage,
            currentUser: null,
            isAuthenticated: false,
            token: null
          });

          return { success: false, error: errorMessage };
        }
      },

      logout: async () => {
        try {
          // Call logout API (optional - even if it fails, we clear local state)
          await authAPI.signOut();
        } catch (error) {
          console.warn('Logout API call failed:', error.message);
        } finally {
          // Always clear local storage and state
          localStorage.removeItem('auth-token');
          localStorage.removeItem('auth-storage');

          set({
            currentUser: null,
            isAuthenticated: false,
            error: null,
            token: null
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // Update user data
      updateUser: (userData) => {
        set((state) => ({
          currentUser: { ...state.currentUser, ...userData }
        }));
      },

      // Initialize auth state from stored token
      initializeAuth: async () => {
        const token = localStorage.getItem('auth-token');
        if (!token) {
          return;
        }

        try {
          // Verify token is still valid
          await authAPI.verifyToken();

          // Token is valid, restore auth state
          set({
            isAuthenticated: true,
            token: token
          });
        } catch (error) {
          // Token is invalid, clear it
          localStorage.removeItem('auth-token');
          localStorage.removeItem('auth-storage');

          set({
            currentUser: null,
            isAuthenticated: false,
            token: null,
            error: null
          });
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        currentUser: state.currentUser,
        isAuthenticated: state.isAuthenticated,
        token: state.token
      })
    }
  )
);

export { useAuthStore };
