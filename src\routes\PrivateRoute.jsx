import { Navigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';

/**
 * PrivateRoute component - Only accessible when authenticated
 * Redirects to login if user is not authenticated
 */
const PrivateRoute = ({ children }) => {
  const { isAuthenticated, currentUser } = useAuthStore((state) => ({
    isAuthenticated: state.isAuthenticated,
    currentUser: state.currentUser
  }));

  console.log('PrivateRoute check:', { isAuthenticated, hasUser: !!currentUser });

  if (!isAuthenticated) {
    console.log('PrivateRoute: Not authenticated, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  console.log('PrivateRoute: Authenticated, rendering children');
  return children;
};

export default PrivateRoute;
