import { Navigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';

/**
 * PrivateRoute component - Only accessible when authenticated
 * Redirects to login if user is not authenticated
 */
const PrivateRoute = ({ children }) => {
  const { isAuthenticated, currentUser, token } = useAuthStore((state) => ({
    isAuthenticated: state.isAuthenticated,
    currentUser: state.currentUser,
    token: state.token
  }));

  // Also check localStorage as a fallback
  const localToken = localStorage.getItem('auth-token');

  console.log('PrivateRoute check:', {
    isAuthenticated,
    hasUser: !!currentUser,
    hasStoreToken: !!token,
    hasLocalToken: !!localToken
  });

  // More defensive check - if we have a token in localStorage but not in store,
  // it might be a timing issue
  const hasValidAuth = isAuthenticated || (localToken && currentUser);

  if (!hasValidAuth) {
    console.log('PrivateRoute: Not authenticated, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  console.log('PrivateRoute: Authenticated, rendering children');
  return children;
};

export default PrivateRoute;
