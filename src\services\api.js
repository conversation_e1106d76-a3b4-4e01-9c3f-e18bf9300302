import axios from 'axios';

// API Configuration
const API_CONFIG = {
  baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://13.234.22.118:8096/somayya-academy/api/v1',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
};

// Create axios instance
const apiClient = axios.create({
  baseURL: API_CONFIG.baseUrl,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle 401 unauthorized errors
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('auth-token');
      localStorage.removeItem('auth-storage');
      window.location.href = '/login';
    }

    // Handle network errors
    if (!error.response) {
      error.message = 'Network error. Please check your connection.';
    }

    return Promise.reject(error);
  }
);

// Auth API endpoints
export const authAPI = {
  /**
   * Sign in to admin portal
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - Encrypted password
   * @returns {Promise} API response with JWT token
   */
  signIn: async (credentials) => {
    const payload = {
      email: credentials.email,
      password: credentials.password, // Already encrypted by auth store
      portalType: 'ADMIN' // Always ADMIN for this portal
    };

    try {
      const response = await apiClient.post('/auth/signin', payload);
      return response.data;
    } catch (error) {
      // Extract error message from response
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          'Login failed';
      throw new Error(errorMessage);
    }
  },

  /**
   * Sign out user
   * @returns {Promise} API response
   */
  signOut: async () => {
    try {
      const response = await apiClient.post('/auth/signout');
      return response.data;
    } catch (error) {
      // Even if signout fails on server, we should clear local storage
      console.warn('Signout API failed:', error.message);
      return { success: true };
    }
  },

  /**
   * Verify token validity
   * @returns {Promise} API response
   */
  verifyToken: async () => {
    try {
      const response = await apiClient.get('/auth/verify');
      return response.data;
    } catch (error) {
      throw new Error('Token verification failed');
    }
  }
};

// Generic API methods for other endpoints
export const api = {
  get: (url, config = {}) => apiClient.get(url, config),
  post: (url, data = {}, config = {}) => apiClient.post(url, data, config),
  put: (url, data = {}, config = {}) => apiClient.put(url, data, config),
  delete: (url, config = {}) => apiClient.delete(url, config),
  patch: (url, data = {}, config = {}) => apiClient.patch(url, data, config),
};

export default apiClient;
