import axios from 'axios';

// API Configuration
const API_CONFIG = {
  baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://13.234.22.118:8096/somayya-academy/api/v1',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
};

// Create axios instance
const apiClient = axios.create({
  baseURL: API_CONFIG.baseUrl,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('API Request with token:', config.url, 'Token:', token.substring(0, 20) + '...');
    }
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
apiClient.interceptors.response.use(
  (response) => {
    // Log successful responses for debugging
    console.log('API Response:', response.config.url, 'Status:', response.status);
    return response;
  },
  (error) => {
    console.error('API Error:', error.config?.url, 'Status:', error.response?.status, 'Message:', error.response?.data?.message);

    // Handle 401 unauthorized errors
    if (error.response?.status === 401) {
      console.warn('Unauthorized access - clearing token and redirecting to login');
      // Clear token and redirect to login
      localStorage.removeItem('auth-token');
      localStorage.removeItem('auth-storage');
      window.location.href = '/login';
    }

    // Handle network errors
    if (!error.response) {
      error.message = 'Network error. Please check your connection.';
    }

    return Promise.reject(error);
  }
);

// Auth API endpoints
export const authAPI = {
  /**
   * Sign in to admin portal
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - Encrypted password
   * @returns {Promise} API response with JWT token
   */
  signIn: async (credentials) => {
    const payload = {
      email: credentials.email,
      password: credentials.password, // Already encrypted by auth store
      portalType: 'ADMIN' // Always ADMIN for this portal
    };

    try {
      const response = await apiClient.post('/auth/signin', payload);

      // Log the response for debugging
      console.log('Login API Response:', response.data);

      // Check if login was successful
      if (response.data.statusCode === 200 && response.data.data?.token) {
        return response.data;
      } else {
        throw new Error(response.data.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login API Error:', error);

      // Extract error message from response
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          'Login failed. Please check your credentials.';
      throw new Error(errorMessage);
    }
  },

  /**
   * Sign out user
   * @returns {Promise} API response
   */
  signOut: async () => {
    try {
      const response = await apiClient.post('/auth/signout');
      return response.data;
    } catch (error) {
      // Even if signout fails on server, we should clear local storage
      console.warn('Signout API failed:', error.message);
      return { success: true };
    }
  },

  /**
   * Verify token validity
   * @returns {Promise} API response
   */
  verifyToken: async () => {
    try {
      const response = await apiClient.get('/auth/verify');
      return response.data;
    } catch (error) {
      console.error('Token verification failed:', error);
      throw new Error('Token verification failed');
    }
  }
};

// Admin API endpoints (examples for testing authenticated requests)
export const adminAPI = {
  /**
   * Get admin dashboard data
   * @returns {Promise} API response
   */
  getDashboardData: async () => {
    try {
      const response = await apiClient.get('/admin/dashboard');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      throw error;
    }
  },

  /**
   * Get admin profile
   * @returns {Promise} API response
   */
  getProfile: async () => {
    try {
      const response = await apiClient.get('/admin/profile');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch admin profile:', error);
      throw error;
    }
  },

  /**
   * Test authenticated endpoint
   * @returns {Promise} API response
   */
  testAuth: async () => {
    try {
      const response = await apiClient.get('/admin/test');
      return response.data;
    } catch (error) {
      console.error('Auth test failed:', error);
      throw error;
    }
  }
};

// Generic API methods for other endpoints
export const api = {
  get: (url, config = {}) => apiClient.get(url, config),
  post: (url, data = {}, config = {}) => apiClient.post(url, data, config),
  put: (url, data = {}, config = {}) => apiClient.put(url, data, config),
  delete: (url, config = {}) => apiClient.delete(url, config),
  patch: (url, data = {}, config = {}) => apiClient.patch(url, data, config),
};

export default apiClient;
